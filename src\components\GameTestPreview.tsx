import React, { useState, useCallback, useEffect } from 'react';
import { useGameStore } from '../store';
import PixiSlotMockup from './mockups/PixiSlotMockup';
import { X, Volume2, VolumeX, Settings, RotateCcw, Monitor, Smartphone } from 'lucide-react';
import SlotGameUI from './visual-journey/slot-animation/SlotGameUI';
import MobilePortraitUI from './visual-journey/slot-animation/MobilePortraitUI';
import MobileLandscapeUI from './visual-journey/slot-animation/MobileLandscapeUI';

interface GameTestPreviewProps {
  onClose: () => void;
  className?: string;
}

/**
 * Game Test Preview Component
 * 
 * Provides a fullscreen playable game preview that allows users to test their game
 * as players would experience it. Features include:
 * - Full game functionality with spinning, betting, and wins
 * - Multiple device view modes (desktop, mobile portrait, mobile landscape)
 * - Real game state management (balance, bets, wins)
 * - Interactive controls and UI elements
 * - Sound controls and settings
 */
const GameTestPreview: React.FC<GameTestPreviewProps> = ({
  onClose,
  className = ''
}) => {
  const { config, triggerAnimation } = useGameStore();
  
  // View mode state
  const [viewMode, setViewMode] = useState<'desktop' | 'mobile-portrait' | 'mobile-landscape'>('desktop');
  const [viewModeChangeKey, setViewModeChangeKey] = useState(0);
  
  // Game state
  const [balance, setBalance] = useState(1000.00);
  const [bet, setBet] = useState(1.00);
  const [win, setWin] = useState(0.00);
  const [isSpinning, setIsSpinning] = useState(false);
  const [isAutoplayActive, setIsAutoplayActive] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  
  // Get current grid configuration
  const reels = config?.reels?.layout?.reels || 5;
  const rows = config?.reels?.layout?.rows || 3;
  const gameName = config?.displayName || config?.name || 'Slot Game';
  
  // Helper function to get symbol URLs from both array and object formats
  const getSymbolUrls = (symbols: string[] | Record<string, string> | undefined): string[] => {
    if (!symbols) return [];
    if (Array.isArray(symbols)) return symbols;
    return Object.values(symbols);
  };

  // Get symbols for preview
  const getSymbolsForPreview = () => {
    const storeSymbols = config?.theme?.generated?.symbols;
    const symbolUrls = getSymbolUrls(storeSymbols);

    return symbolUrls.map((symbol: any) => {
      if (typeof symbol === 'string') {
        return symbol;
      }
      return symbol.url || symbol.imageUrl;
    }).filter(Boolean);
  };

  // Get background and frame
  const background = config?.theme?.generated?.background;
  const frame = config?.theme?.generated?.frame;
  
  // Get generated UI buttons for overlay components
  const getCustomButtons = () => {
    const extractedButtons = (config as any)?.extractedUIButtons || (config as any)?.uiElements;
    if (!extractedButtons) return undefined;

    return {
      spinButton: extractedButtons.spinButton || extractedButtons.SPIN,
      autoplayButton: extractedButtons.autoplayButton || extractedButtons.AUTO,
      menuButton: extractedButtons.menuButton || extractedButtons.MENU,
      soundButton: extractedButtons.soundButton || extractedButtons.SOUND,
      settingsButton: extractedButtons.settingsButton || extractedButtons.SETTINGS,
      quickButton: extractedButtons.quickButton || extractedButtons.QUICK
    };
  };

  const customButtons = getCustomButtons();
  const symbols = getSymbolsForPreview();

  // Game interaction handlers
  const handleSpin = useCallback(() => {
    if (isSpinning || balance < bet) {
      console.log('[GameTestPreview] Spin blocked - isSpinning:', isSpinning, 'balance:', balance, 'bet:', bet);
      return;
    }

    console.log('[GameTestPreview] Spin clicked - starting game spin');

    // Deduct bet from balance
    setBalance(prev => prev - bet);
    setIsSpinning(true);
    setWin(0); // Reset previous win

    // Trigger animation for game store listeners
    triggerAnimation('small-win');

    // Trigger the PixiJS slot spin animation
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('pixiSlotSpin'));
    }, 10);

    // Simulate spin duration and result with more realistic game mechanics
    const spinDuration = 2000 + Math.random() * 2000; // 2-4 second spin duration

    setTimeout(() => {
      // Enhanced win calculation with more realistic slot mechanics
      const winChance = Math.random();
      let winAmount = 0;
      let winType = 'small-win';

      if (winChance > 0.65) { // 35% chance to win (more realistic)
        if (winChance > 0.98) { // 2% chance for mega win
          winAmount = bet * (50 + Math.random() * 100); // 50x to 150x bet
          winType = 'mega-win';
        } else if (winChance > 0.94) { // 4% chance for big win
          winAmount = bet * (10 + Math.random() * 40); // 10x to 50x bet
          winType = 'big-win';
        } else if (winChance > 0.82) { // 12% chance for medium win
          winAmount = bet * (3 + Math.random() * 7); // 3x to 10x bet
          winType = 'big-win';
        } else { // 17% chance for small win
          winAmount = bet * (0.2 + Math.random() * 2.8); // 0.2x to 3x bet
          winType = 'small-win';
        }
      }

      if (winAmount > 0) {
        setWin(winAmount);
        setBalance(prev => prev + winAmount);

        // Trigger appropriate win animation
        triggerAnimation(winType as 'small-win' | 'big-win' | 'mega-win');

        console.log('[GameTestPreview] Win!', { amount: winAmount, type: winType, multiplier: (winAmount / bet).toFixed(2) + 'x' });
      }

      setIsSpinning(false);
    }, spinDuration);
  }, [isSpinning, balance, bet, triggerAnimation]);

  const handleAutoplayToggle = useCallback(() => {
    setIsAutoplayActive(prev => {
      const newAutoplayState = !prev;
      console.log('[GameTestPreview] Autoplay toggled:', newAutoplayState);
      return newAutoplayState;
    });
  }, []);

  // Autoplay effect
  useEffect(() => {
    let autoplayInterval: NodeJS.Timeout;

    if (isAutoplayActive && !isSpinning && balance >= bet) {
      autoplayInterval = setTimeout(() => {
        handleSpin();
      }, 1000); // 1 second delay between autoplay spins
    }

    return () => {
      if (autoplayInterval) {
        clearTimeout(autoplayInterval);
      }
    };
  }, [isAutoplayActive, isSpinning, balance, bet, handleSpin]);

  const handleSoundToggle = useCallback(() => {
    setIsMuted(prev => !prev);
  }, []);

  const handleBetChange = useCallback(() => {
    // Cycle through common bet values
    setBet(prev => {
      const betValues = [0.25, 0.50, 1.00, 2.00, 5.00, 10.00, 25.00, 50.00];
      const currentIndex = betValues.indexOf(prev);
      return betValues[(currentIndex + 1) % betValues.length];
    });
  }, []);

  const handleSettingsToggle = useCallback(() => {
    setShowSettings(prev => !prev);
  }, []);

  const handleReset = useCallback(() => {
    setBalance(1000.00);
    setBet(1.00);
    setWin(0.00);
    setIsSpinning(false);
    setIsAutoplayActive(false);
  }, []);

  // Handle view mode changes
  const handleViewModeChange = (mode: 'desktop' | 'mobile-portrait' | 'mobile-landscape') => {
    setViewMode(mode);
    setViewModeChangeKey(prev => prev + 1);
  };

  // Handle escape key to close
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  return (
    <div className={`fixed inset-0 z-50 bg-black ${className}`}>
      {/* Header Controls */}
      <div className="absolute top-0 left-0 right-0 z-50 bg-black bg-opacity-75 backdrop-blur-sm">
        <div className="flex items-center justify-between p-4">
          {/* Left: Game Info */}
          <div className="flex items-center gap-4">
            <h1 className="text-white text-xl font-bold">{gameName}</h1>
            <div className="text-gray-300 text-sm">
              Test Mode • Balance: ${balance.toFixed(2)} • Bet: ${bet.toFixed(2)}
              {win > 0 && <span className="text-green-400 ml-2">• Win: ${win.toFixed(2)}</span>}
            </div>
          </div>

          {/* Center: View Mode Controls */}
          <div className="flex items-center gap-2 bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => handleViewModeChange('desktop')}
              className={`flex items-center gap-2 px-3 py-2 rounded-md transition-all ${
                viewMode === 'desktop'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700'
              }`}
            >
              <Monitor className="w-4 h-4" />
              Desktop
            </button>
            <button
              onClick={() => handleViewModeChange('mobile-portrait')}
              className={`flex items-center gap-2 px-3 py-2 rounded-md transition-all ${
                viewMode === 'mobile-portrait'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700'
              }`}
            >
              <Smartphone className="w-4 h-4" />
              Mobile
            </button>
            <button
              onClick={() => handleViewModeChange('mobile-landscape')}
              className={`flex items-center gap-2 px-3 py-2 rounded-md transition-all ${
                viewMode === 'mobile-landscape'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700'
              }`}
            >
              <Smartphone className="w-4 h-4 rotate-90" />
              Landscape
            </button>
          </div>

          {/* Right: Controls */}
          <div className="flex items-center gap-2">
            <button
              onClick={handleSoundToggle}
              className="p-2 rounded-lg bg-gray-800 text-gray-300 hover:text-white hover:bg-gray-700 transition-all"
              title={isMuted ? 'Unmute' : 'Mute'}
            >
              {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
            </button>
            
            <button
              onClick={handleReset}
              className="p-2 rounded-lg bg-gray-800 text-gray-300 hover:text-white hover:bg-gray-700 transition-all"
              title="Reset Game State"
            >
              <RotateCcw className="w-5 h-5" />
            </button>
            
            <button
              onClick={handleSettingsToggle}
              className="p-2 rounded-lg bg-gray-800 text-gray-300 hover:text-white hover:bg-gray-700 transition-all"
              title="Settings"
            >
              <Settings className="w-5 h-5" />
            </button>
            
            <button
              onClick={onClose}
              className="p-2 rounded-lg bg-red-600 text-white hover:bg-red-700 transition-all"
              title="Close Test Mode"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Game Content */}
      <div className="w-full h-full pt-20 flex items-center justify-center">
        {viewMode === 'desktop' ? (
          // Desktop View
          <div className="w-full h-full max-w-7xl mx-auto p-4">
            <div className="relative w-full h-full bg-gray-900 rounded-lg overflow-hidden">
              <PixiSlotMockup
                key={`desktop-${viewModeChangeKey}`}
                cols={reels}
                rows={rows}
                symbols={symbols}
                background={background}
                frame={frame}
                showControls={false}
                isMobile={false}
                enableSpinAnimation={true}
                spinTriggerEvent="pixiSlotSpin"
                onSpin={handleSpin}
                onBalanceChange={setBalance}
                initialBalance={balance}
                initialBet={bet}
                className="w-full h-full"
              />

              {/* Desktop UI Overlay */}
              <div className="absolute inset-0 z-30 pointer-events-none">
                <SlotGameUI
                  onSpin={handleSpin}
                  onAutoplayToggle={handleAutoplayToggle}
                  onSoundToggle={handleSoundToggle}
                  onBetChange={handleBetChange}
                  balance={balance}
                  bet={bet}
                  win={win}
                  isSpinning={isSpinning}
                  isAutoplayActive={isAutoplayActive}
                  isMuted={isMuted}
                  className="pointer-events-auto"
                  customButtons={customButtons}
                />
              </div>
            </div>
          </div>
        ) : viewMode === 'mobile-portrait' ? (
          // Mobile Portrait View
          <div className="w-full h-full flex items-center justify-center">
            <div className="relative" style={{ width: '375px', height: '667px' }}>
              {/* Phone Frame */}
              <div className="absolute inset-0 bg-gray-900 rounded-[2.5rem] p-2 shadow-2xl">
                <div className="w-full h-full bg-black rounded-[2rem] overflow-hidden relative">
                  <PixiSlotMockup
                    key={`mobile-portrait-${viewModeChangeKey}`}
                    cols={reels}
                    rows={rows}
                    symbols={symbols}
                    background={background}
                    frame={frame}
                    showControls={false}
                    isMobile={true}
                    orientation="portrait"
                    enableSpinAnimation={true}
                    spinTriggerEvent="pixiSlotSpin"
                    onSpin={handleSpin}
                    onBalanceChange={setBalance}
                    initialBalance={balance}
                    initialBet={bet}
                    className="w-full h-full"
                  />

                  {/* Mobile Portrait UI Overlay */}
                  <div className="absolute inset-0 z-30 pointer-events-none">
                    <MobilePortraitUI
                      onSpin={handleSpin}
                      onAutoplayToggle={handleAutoplayToggle}
                      onSoundToggle={handleSoundToggle}
                      onBetChange={handleBetChange}
                      balance={balance}
                      bet={bet}
                      win={win}
                      className="pointer-events-auto"
                      customButtons={customButtons}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Mobile Landscape View
          <div className="w-full h-full flex items-center justify-center">
            <div className="relative" style={{ width: '667px', height: '375px' }}>
              {/* Phone Frame */}
              <div className="absolute inset-0 bg-gray-900 rounded-[2.5rem] p-2 shadow-2xl">
                <div className="w-full h-full bg-black rounded-[2rem] overflow-hidden relative">
                  <PixiSlotMockup
                    key={`mobile-landscape-${viewModeChangeKey}`}
                    cols={reels}
                    rows={rows}
                    symbols={symbols}
                    background={background}
                    frame={frame}
                    showControls={false}
                    isMobile={true}
                    orientation="landscape"
                    enableSpinAnimation={true}
                    spinTriggerEvent="pixiSlotSpin"
                    onSpin={handleSpin}
                    onBalanceChange={setBalance}
                    initialBalance={balance}
                    initialBet={bet}
                    className="w-full h-full"
                  />

                  {/* Mobile Landscape UI Overlay */}
                  <div className="absolute inset-0 z-30 pointer-events-none">
                    <MobileLandscapeUI
                      onSpin={handleSpin}
                      onAutoplayToggle={handleAutoplayToggle}
                      onMenuToggle={() => {}}
                      onSoundToggle={handleSoundToggle}
                      onBetChange={handleBetChange}
                      balance={balance}
                      bet={bet}
                      win={win}
                      className="pointer-events-auto"
                      customButtons={customButtons}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="absolute top-20 right-4 bg-gray-800 rounded-lg p-4 shadow-xl z-50 min-w-64">
          <h3 className="text-white font-semibold mb-3">Game Settings</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Sound</span>
              <button
                onClick={handleSoundToggle}
                className={`w-12 h-6 rounded-full transition-all ${
                  isMuted ? 'bg-gray-600' : 'bg-blue-600'
                }`}
              >
                <div className={`w-5 h-5 bg-white rounded-full transition-transform ${
                  isMuted ? 'translate-x-0.5' : 'translate-x-6'
                }`} />
              </button>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Autoplay</span>
              <button
                onClick={handleAutoplayToggle}
                className={`w-12 h-6 rounded-full transition-all ${
                  isAutoplayActive ? 'bg-blue-600' : 'bg-gray-600'
                }`}
              >
                <div className={`w-5 h-5 bg-white rounded-full transition-transform ${
                  isAutoplayActive ? 'translate-x-6' : 'translate-x-0.5'
                }`} />
              </button>
            </div>
            <div className="pt-2 border-t border-gray-700">
              <button
                onClick={handleReset}
                className="w-full px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-all"
              >
                Reset Game
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GameTestPreview;
