import React, { useState, useCallback, useEffect } from 'react';
import { useGameStore } from '../store';
import PixiSlotMockup from './mockups/PixiSlotMockup';
import { X, Volume2, VolumeX, Settings, RotateCcw, Monitor, Smartphone } from 'lucide-react';
import SlotGameUI from './visual-journey/slot-animation/SlotGameUI';
import MobilePortraitUI from './visual-journey/slot-animation/MobilePortraitUI';
import MobileLandscapeUI from './visual-journey/slot-animation/MobileLandscapeUI';

interface GameTestPreviewProps {
  onClose: () => void;
  className?: string;
}

/**
 * Game Test Preview Component
 * 
 * Provides a fullscreen playable game preview that allows users to test their game
 * as players would experience it. Features include:
 * - Full game functionality with spinning, betting, and wins
 * - Multiple device view modes (desktop, mobile portrait, mobile landscape)
 * - Real game state management (balance, bets, wins)
 * - Interactive controls and UI elements
 * - Sound controls and settings
 */
const GameTestPreview: React.FC<GameTestPreviewProps> = ({
  onClose,
  className = ''
}) => {
  const { config, triggerAnimation } = useGameStore();
  
  // View mode state
  const [viewMode, setViewMode] = useState<'desktop' | 'mobile-portrait' | 'mobile-landscape'>('desktop');
  const [viewModeChangeKey, setViewModeChangeKey] = useState(0);
  
  // Game state
  const [balance, setBalance] = useState(1000.00);
  const [bet, setBet] = useState(1.00);
  const [win, setWin] = useState(0.00);
  const [isSpinning, setIsSpinning] = useState(false);
  const [isAutoplayActive, setIsAutoplayActive] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  
  // Get current grid configuration
  const reels = config?.reels?.layout?.reels || 5;
  const rows = config?.reels?.layout?.rows || 3;
  const gameName = config?.displayName || config?.name || 'Slot Game';
  
  // Helper function to get symbol URLs from both array and object formats
  const getSymbolUrls = (symbols: string[] | Record<string, string> | undefined): string[] => {
    if (!symbols) return [];
    if (Array.isArray(symbols)) return symbols;
    return Object.values(symbols);
  };

  // Get symbols for preview
  const getSymbolsForPreview = () => {
    const storeSymbols = config?.theme?.generated?.symbols;
    const symbolUrls = getSymbolUrls(storeSymbols);

    return symbolUrls.map((symbol: any) => {
      if (typeof symbol === 'string') {
        return symbol;
      }
      return symbol.url || symbol.imageUrl;
    }).filter(Boolean);
  };

  // Get background and frame
  const background = config?.theme?.generated?.background;
  const frame = config?.theme?.generated?.frame;
  
  // Get generated UI buttons for overlay components
  const getCustomButtons = () => {
    const extractedButtons = (config as any)?.extractedUIButtons || (config as any)?.uiElements;
    if (!extractedButtons) return undefined;

    return {
      spinButton: extractedButtons.spinButton || extractedButtons.SPIN,
      autoplayButton: extractedButtons.autoplayButton || extractedButtons.AUTO,
      menuButton: extractedButtons.menuButton || extractedButtons.MENU,
      soundButton: extractedButtons.soundButton || extractedButtons.SOUND,
      settingsButton: extractedButtons.settingsButton || extractedButtons.SETTINGS,
      quickButton: extractedButtons.quickButton || extractedButtons.QUICK
    };
  };

  const customButtons = getCustomButtons();
  const symbols = getSymbolsForPreview();

  // Game interaction handlers
  const handleSpin = useCallback(() => {
    if (isSpinning || balance < bet) {
      console.log('[GameTestPreview] Spin blocked - isSpinning:', isSpinning, 'balance:', balance, 'bet:', bet);
      return;
    }

    console.log('[GameTestPreview] Spin clicked - starting game spin');

    // Deduct bet from balance
    setBalance(prev => prev - bet);
    setIsSpinning(true);
    setWin(0); // Reset previous win

    // Trigger animation for game store listeners
    triggerAnimation('small-win');

    // Trigger the PixiJS slot spin animation
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('pixiSlotSpin'));
    }, 10);

    // Simulate spin duration and result with more realistic game mechanics
    const spinDuration = 2000 + Math.random() * 2000; // 2-4 second spin duration

    setTimeout(() => {
      // Enhanced win calculation with more realistic slot mechanics
      const winChance = Math.random();
      let winAmount = 0;
      let winType = 'small-win';

      if (winChance > 0.65) { // 35% chance to win (more realistic)
        if (winChance > 0.98) { // 2% chance for mega win
          winAmount = bet * (50 + Math.random() * 100); // 50x to 150x bet
          winType = 'mega-win';
        } else if (winChance > 0.94) { // 4% chance for big win
          winAmount = bet * (10 + Math.random() * 40); // 10x to 50x bet
          winType = 'big-win';
        } else if (winChance > 0.82) { // 12% chance for medium win
          winAmount = bet * (3 + Math.random() * 7); // 3x to 10x bet
          winType = 'big-win';
        } else { // 17% chance for small win
          winAmount = bet * (0.2 + Math.random() * 2.8); // 0.2x to 3x bet
          winType = 'small-win';
        }
      }

      if (winAmount > 0) {
        setWin(winAmount);
        setBalance(prev => prev + winAmount);

        // Trigger appropriate win animation
        triggerAnimation(winType as 'small-win' | 'big-win' | 'mega-win');

        console.log('[GameTestPreview] Win!', { amount: winAmount, type: winType, multiplier: (winAmount / bet).toFixed(2) + 'x' });
      }

      setIsSpinning(false);
    }, spinDuration);
  }, [isSpinning, balance, bet, triggerAnimation]);

  const handleAutoplayToggle = useCallback(() => {
    setIsAutoplayActive(prev => {
      const newAutoplayState = !prev;
      console.log('[GameTestPreview] Autoplay toggled:', newAutoplayState);
      return newAutoplayState;
    });
  }, []);

  // Autoplay effect
  useEffect(() => {
    let autoplayInterval: NodeJS.Timeout;

    if (isAutoplayActive && !isSpinning && balance >= bet) {
      autoplayInterval = setTimeout(() => {
        handleSpin();
      }, 1000); // 1 second delay between autoplay spins
    }

    return () => {
      if (autoplayInterval) {
        clearTimeout(autoplayInterval);
      }
    };
  }, [isAutoplayActive, isSpinning, balance, bet, handleSpin]);

  const handleSoundToggle = useCallback(() => {
    setIsMuted(prev => !prev);
  }, []);

  const handleBetChange = useCallback(() => {
    // Cycle through common bet values
    setBet(prev => {
      const betValues = [0.25, 0.50, 1.00, 2.00, 5.00, 10.00, 25.00, 50.00];
      const currentIndex = betValues.indexOf(prev);
      return betValues[(currentIndex + 1) % betValues.length];
    });
  }, []);

  const handleReset = useCallback(() => {
    setBalance(1000.00);
    setBet(1.00);
    setWin(0.00);
    setIsSpinning(false);
    setIsAutoplayActive(false);
  }, []);

  // Handle view mode changes
  const handleViewModeChange = (mode: 'desktop' | 'mobile-portrait' | 'mobile-landscape') => {
    setViewMode(mode);
    setViewModeChangeKey(prev => prev + 1);
  };

  // Handle escape key to close
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  return (
    <div className={`fixed inset-0 z-50 bg-black ${className}`}>
      {/* Minimal Header Controls */}
      <div className="absolute top-0 left-0 right-0 z-50 bg-black bg-opacity-50">
        <div className="flex items-center justify-between p-2">
          {/* Left: Game Info */}
          <div className="flex items-center gap-3">
            <span className="text-white text-sm font-medium">{gameName}</span>
            <span className="text-gray-400 text-xs">Test Mode</span>
          </div>

          {/* Center: View Mode Controls */}
          <div className="flex items-center gap-1 bg-black bg-opacity-50 rounded-md p-1">
            <button
              onClick={() => handleViewModeChange('desktop')}
              className={`p-1.5 rounded transition-all ${
                viewMode === 'desktop'
                  ? 'bg-white text-black'
                  : 'text-gray-300 hover:text-white'
              }`}
              title="Desktop View"
            >
              <Monitor className="w-4 h-4" />
            </button>
            <button
              onClick={() => handleViewModeChange('mobile-portrait')}
              className={`p-1.5 rounded transition-all ${
                viewMode === 'mobile-portrait'
                  ? 'bg-white text-black'
                  : 'text-gray-300 hover:text-white'
              }`}
              title="Mobile Portrait"
            >
              <Smartphone className="w-4 h-4" />
            </button>
            <button
              onClick={() => handleViewModeChange('mobile-landscape')}
              className={`p-1.5 rounded transition-all ${
                viewMode === 'mobile-landscape'
                  ? 'bg-white text-black'
                  : 'text-gray-300 hover:text-white'
              }`}
              title="Mobile Landscape"
            >
              <Smartphone className="w-4 h-4 rotate-90" />
            </button>
          </div>

          {/* Right: Essential Controls */}
          <div className="flex items-center gap-1">
            <button
              onClick={handleReset}
              className="p-1.5 rounded bg-black bg-opacity-50 text-gray-300 hover:text-white transition-all"
              title="Reset Game"
            >
              <RotateCcw className="w-4 h-4" />
            </button>

            <button
              onClick={onClose}
              className="p-1.5 rounded bg-red-600 bg-opacity-80 text-white hover:bg-red-600 transition-all"
              title="Exit Test Mode"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Game Content */}
      <div className="w-full h-full pt-12 bg-black">
        {viewMode === 'desktop' ? (
          // Desktop View - Full Game Experience
          <div className="w-full h-full relative">
            <PixiSlotMockup
              key={`desktop-${viewModeChangeKey}`}
              cols={reels}
              rows={rows}
              symbols={symbols}
              background={background}
              frame={frame}
              showControls={true}
              isMobile={false}
              enableSpinAnimation={true}
              spinTriggerEvent="pixiSlotSpin"
              onSpin={handleSpin}
              onBalanceChange={setBalance}
              initialBalance={balance}
              initialBet={bet}
              className="w-full h-full"
            />
          </div>
        ) : viewMode === 'mobile-portrait' ? (
          // Mobile Portrait View - Full Screen Mobile Experience
          <div className="w-full h-full flex items-center justify-center bg-black">
            <div className="relative w-full h-full max-w-md">
              <PixiSlotMockup
                key={`mobile-portrait-${viewModeChangeKey}`}
                cols={reels}
                rows={rows}
                symbols={symbols}
                background={background}
                frame={frame}
                showControls={true}
                isMobile={true}
                orientation="portrait"
                enableSpinAnimation={true}
                spinTriggerEvent="pixiSlotSpin"
                onSpin={handleSpin}
                onBalanceChange={setBalance}
                initialBalance={balance}
                initialBet={bet}
                className="w-full h-full"
              />
            </div>
          </div>
        ) : (
          // Mobile Landscape View - Full Screen Landscape Experience
          <div className="w-full h-full flex items-center justify-center bg-black">
            <div className="relative w-full h-full">
              <PixiSlotMockup
                key={`mobile-landscape-${viewModeChangeKey}`}
                cols={reels}
                rows={rows}
                symbols={symbols}
                background={background}
                frame={frame}
                showControls={true}
                isMobile={true}
                orientation="landscape"
                enableSpinAnimation={true}
                spinTriggerEvent="pixiSlotSpin"
                onSpin={handleSpin}
                onBalanceChange={setBalance}
                initialBalance={balance}
                initialBet={bet}
                className="w-full h-full"
              />
            </div>
          </div>
        )}
      </div>


    </div>
  );
};

export default GameTestPreview;
