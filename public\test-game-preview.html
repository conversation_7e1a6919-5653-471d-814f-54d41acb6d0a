<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Test Preview - Test Page</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-info {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button.secondary {
            background: #2196F3;
        }
        .test-button.secondary:hover {
            background: #1976D2;
        }
        .instructions {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .instructions h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 5px 0;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 2px solid #444;
            border-radius: 8px;
            background: white;
        }
        .status {
            background: #444;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎰 Game Test Preview - Testing Interface</h1>
        
        <div class="test-info">
            <h2>Test the New Game Preview Functionality</h2>
            <p>This page helps test the new Game Test Preview component that replaces the edit mode when clicking "Test Game".</p>
            
            <div class="instructions">
                <h3>How to Test:</h3>
                <ul>
                    <li><strong>Navigate to Game Creation:</strong> Go to the main app and start creating a slot game</li>
                    <li><strong>Reach Step 7+:</strong> The "Test Game" button appears from step 7 onwards</li>
                    <li><strong>Click "Test Game":</strong> Should open the new fullscreen game preview instead of edit mode</li>
                    <li><strong>Test Game Features:</strong> Try spinning, changing bets, switching view modes, etc.</li>
                    <li><strong>Test All View Modes:</strong> Desktop, Mobile Portrait, Mobile Landscape</li>
                    <li><strong>Test Game Mechanics:</strong> Balance, betting, wins, autoplay, sound controls</li>
                </ul>
            </div>
            
            <div class="instructions">
                <h3>Expected Features in Game Test Preview:</h3>
                <ul>
                    <li>✅ Fullscreen game preview with close button</li>
                    <li>✅ Three view modes: Desktop, Mobile Portrait, Mobile Landscape</li>
                    <li>✅ Real game mechanics: balance, betting, wins</li>
                    <li>✅ Interactive controls: spin, autoplay, sound, settings</li>
                    <li>✅ Realistic win calculations with different win types</li>
                    <li>✅ Game state management (balance starts at $1000, bet cycling)</li>
                    <li>✅ Escape key to close, reset functionality</li>
                    <li>✅ PixiJS slot animations and visual effects</li>
                </ul>
            </div>
        </div>

        <div class="status" id="status">
            Status: Ready for testing
        </div>

        <button class="test-button" onclick="openMainApp()">
            🚀 Open Main App for Testing
        </button>
        
        <button class="test-button secondary" onclick="openStepDirect()">
            📋 Go Directly to Step 7 (Test Game Available)
        </button>
        
        <button class="test-button secondary" onclick="refreshPage()">
            🔄 Refresh This Page
        </button>

        <div class="instructions">
            <h3>Testing Checklist:</h3>
            <div id="checklist">
                <label><input type="checkbox" id="check1"> Game Test Preview opens in fullscreen</label><br>
                <label><input type="checkbox" id="check2"> Desktop view mode works correctly</label><br>
                <label><input type="checkbox" id="check3"> Mobile portrait view mode works correctly</label><br>
                <label><input type="checkbox" id="check4"> Mobile landscape view mode works correctly</label><br>
                <label><input type="checkbox" id="check5"> Spin button works and deducts balance</label><br>
                <label><input type="checkbox" id="check6"> Win calculations work and add to balance</label><br>
                <label><input type="checkbox" id="check7"> Bet cycling works (0.25, 0.50, 1.00, etc.)</label><br>
                <label><input type="checkbox" id="check8"> Autoplay functionality works</label><br>
                <label><input type="checkbox" id="check9"> Sound controls work</label><br>
                <label><input type="checkbox" id="check10"> Settings panel opens and works</label><br>
                <label><input type="checkbox" id="check11"> Reset button restores initial state</label><br>
                <label><input type="checkbox" id="check12"> Close button (X) exits preview</label><br>
                <label><input type="checkbox" id="check13"> Escape key exits preview</label><br>
                <label><input type="checkbox" id="check14"> PixiJS animations play correctly</label><br>
                <label><input type="checkbox" id="check15"> Game symbols and assets display correctly</label><br>
            </div>
        </div>

        <div class="instructions">
            <h3>🔧 Troubleshooting:</h3>
            <ul>
                <li><strong>If Test Game button doesn't appear:</strong> Make sure you're on step 7 or later</li>
                <li><strong>If preview doesn't open:</strong> Check browser console for errors</li>
                <li><strong>If symbols don't load:</strong> Ensure game has generated symbols in previous steps</li>
                <li><strong>If animations don't work:</strong> Check PixiJS compatibility and WebGL support</li>
            </ul>
        </div>
    </div>

    <script>
        function updateStatus(message) {
            document.getElementById('status').textContent = `Status: ${message}`;
        }

        function openMainApp() {
            updateStatus('Opening main application...');
            window.open('/', '_blank');
        }

        function openStepDirect() {
            updateStatus('Opening step 7 directly...');
            // Try to open directly to a step where Test Game is available
            window.open('/#/create?step=6&force=true', '_blank');
        }

        function refreshPage() {
            updateStatus('Refreshing page...');
            window.location.reload();
        }

        // Save checklist state
        function saveChecklistState() {
            const checkboxes = document.querySelectorAll('#checklist input[type="checkbox"]');
            const state = {};
            checkboxes.forEach((checkbox, index) => {
                state[`check${index + 1}`] = checkbox.checked;
            });
            localStorage.setItem('gameTestChecklist', JSON.stringify(state));
        }

        // Load checklist state
        function loadChecklistState() {
            const saved = localStorage.getItem('gameTestChecklist');
            if (saved) {
                const state = JSON.parse(saved);
                Object.keys(state).forEach(key => {
                    const checkbox = document.getElementById(key);
                    if (checkbox) {
                        checkbox.checked = state[key];
                    }
                });
            }
        }

        // Add event listeners to checkboxes
        document.addEventListener('DOMContentLoaded', function() {
            loadChecklistState();
            
            const checkboxes = document.querySelectorAll('#checklist input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', saveChecklistState);
            });
            
            updateStatus('Ready for testing - Use the buttons above to start');
        });
    </script>
</body>
</html>
